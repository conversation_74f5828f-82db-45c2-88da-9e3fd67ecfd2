"""
Tests for the Markdown parser.

These tests fall under the "Parser accuracy for different languages" category
from the testing strategy in INDEXER.md and use small synthetic codebases
for test data.
"""

from pathlib import Path

from parsers.markdown import MarkdownParser
from parsers.base import ParseResult


class TestMarkdownParser:
    """Test suite for MarkdownParser."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = MarkdownParser()

    def test_get_language(self):
        """Test that parser returns correct language identifier."""
        assert self.parser.get_language() == "markdown"

    def test_get_supported_extensions(self):
        """Test that parser returns correct supported extensions."""
        extensions = self.parser.get_supported_extensions()
        expected = [".md", ".markdown", ".mdown", ".mkd", ".rst"]
        assert extensions == expected

    def test_parse_simple_markdown(self):
        """Test parsing simple markdown with headers."""
        content = """# Main Title

This is the introduction paragraph.

## Section 1

Content for section 1.

### Subsection 1.1

More detailed content.

## Section 2

Content for section 2.
"""
        file_path = Path("test.md")
        result = self.parser.parse(content, file_path)

        assert isinstance(result, ParseResult)
        assert len(result.chunks) == 4  # 4 sections
        assert result.metadata["language"] == "markdown"
        assert result.metadata["file_type"] == "markdown"
        assert result.metadata["total_chunks"] == 4
        assert len(result.errors) == 0

        # Check first chunk (Main Title)
        first_chunk = result.chunks[0]
        assert first_chunk["metadata"]["header_level"] == 1
        assert first_chunk["metadata"]["header_title"] == "Main Title"
        assert first_chunk["metadata"]["chunk_type"] == "documentation_section"
        assert "This is the introduction paragraph." in first_chunk["content"]

    def test_parse_markdown_with_code_blocks(self):
        """Test parsing markdown with fenced code blocks."""
        content = """# Code Examples

Here's a Python example:

```python
def hello_world():
    print("Hello, World!")
    return True
```

And here's some JavaScript:

```javascript
function greet(name) {
    console.log(`Hello, ${name}!`);
}
```

Some indented code:

    def simple_function():
        return 42
"""
        file_path = Path("code_examples.md")
        result = self.parser.parse(content, file_path)

        assert len(result.chunks) == 1
        chunk = result.chunks[0]
        code_blocks = chunk["metadata"]["code_blocks"]

        assert len(code_blocks) >= 3  # May have more due to parsing behavior

        # Find the different types of code blocks
        fenced_blocks = [block for block in code_blocks if block["type"] == "fenced"]
        indented_blocks = [block for block in code_blocks if block["type"] == "indented"]

        assert len(fenced_blocks) >= 2  # Python and JavaScript
        assert len(indented_blocks) >= 1  # Indented code

        # Check that we have Python and JavaScript blocks
        languages = {block["language"] for block in fenced_blocks}
        assert "python" in languages
        assert "javascript" in languages

        # Check content exists in blocks
        all_code = " ".join(block["code"] for block in code_blocks)
        assert "def hello_world():" in all_code
        assert "function greet(name)" in all_code
        assert "def simple_function():" in all_code

    def test_parse_markdown_with_links(self):
        """Test parsing markdown with various link types."""
        content = """# Links Example

Here's a [regular link](https://example.com) and a [reference link][ref1].

Auto link: <https://github.com>

[ref1]: https://reference.example.com
"""
        file_path = Path("links.md")
        result = self.parser.parse(content, file_path)

        chunk = result.chunks[0]
        links = chunk["metadata"]["links"]

        assert len(links) == 3

        # Check regular link
        regular_link = links[0]
        assert regular_link["text"] == "regular link"
        assert regular_link["url"] == "https://example.com"
        assert regular_link["type"] == "markdown"

        # Check reference link
        ref_link = links[1]
        assert ref_link["text"] == "reference link"
        assert ref_link["reference"] == "ref1"
        assert ref_link["type"] == "reference"

        # Check auto link
        auto_link = links[2]
        assert auto_link["text"] == "https://github.com"
        assert auto_link["url"] == "https://github.com"
        assert auto_link["type"] == "auto"

    def test_parse_setext_headers(self):
        """Test parsing setext-style headers (underlined with = and -)."""
        content = """Main Title
==========

This is under the main title.

Subtitle
--------

This is under the subtitle.
"""
        file_path = Path("setext.md")
        result = self.parser.parse(content, file_path)

        assert len(result.chunks) == 2

        # Check H1 header
        first_chunk = result.chunks[0]
        assert first_chunk["metadata"]["header_level"] == 1
        assert first_chunk["metadata"]["header_title"] == "Main Title"

        # Check H2 header
        second_chunk = result.chunks[1]
        assert second_chunk["metadata"]["header_level"] == 2
        assert second_chunk["metadata"]["header_title"] == "Subtitle"

    def test_parse_no_headers(self):
        """Test parsing markdown without headers (single chunk)."""
        content = """This is a simple markdown file without any headers.

It has multiple paragraphs and some **bold text** and *italic text*.

But no headers at all.
"""
        file_path = Path("no_headers.md")
        result = self.parser.parse(content, file_path)

        assert len(result.chunks) == 1
        chunk = result.chunks[0]
        assert chunk["metadata"]["chunk_type"] == "documentation_file"
        # Line count may vary based on trailing newlines
        assert chunk["metadata"]["total_lines"] >= 5
        assert chunk["content"] == content

    def test_parse_rst_file(self):
        """Test parsing reStructuredText files."""
        content = """Title
=====

This is a reStructuredText file.

Subtitle
--------

With some content.
"""
        file_path = Path("test.rst")
        result = self.parser.parse(content, file_path)

        assert len(result.chunks) == 1
        assert result.metadata["file_type"] == "rst"
        chunk = result.chunks[0]
        assert chunk["metadata"]["language"] == "rst"
        assert chunk["content"] == content

    def test_parse_empty_file(self):
        """Test parsing empty markdown file."""
        content = ""
        file_path = Path("empty.md")
        result = self.parser.parse(content, file_path)

        assert len(result.chunks) == 1
        chunk = result.chunks[0]
        assert chunk["content"] == ""
        assert chunk["metadata"]["chunk_type"] == "documentation_file"

    def test_parse_only_whitespace(self):
        """Test parsing file with only whitespace."""
        content = "   \n\n  \t  \n"
        file_path = Path("whitespace.md")
        result = self.parser.parse(content, file_path)

        assert len(result.chunks) == 1
        chunk = result.chunks[0]
        assert chunk["content"] == content

    def test_parse_complex_markdown(self):
        """Test parsing complex markdown with multiple elements."""
        content = """# Project Documentation

Welcome to our project!

## Installation

To install, run:

```bash
pip install our-package
```

## Usage

Here's how to use it:

```python
import our_package

# Create instance
instance = our_package.MyClass()
result = instance.process()
```

### Configuration

You can configure using:

- Option 1: Environment variables
- Option 2: Config file
- Option 3: Command line args

## Links

- [Documentation](https://docs.example.com)
- [GitHub](https://github.com/example/repo)
- <https://example.com>

## Tables

| Feature | Status |
|---------|--------|
| Auth    | ✅     |
| API     | 🚧     |
"""
        file_path = Path("complex.md")
        result = self.parser.parse(content, file_path)

        assert len(result.chunks) >= 4  # At least 4 main sections
        assert len(result.errors) == 0

        # Check that code blocks are extracted from various sections
        all_code_blocks = []
        for chunk in result.chunks:
            all_code_blocks.extend(chunk["metadata"]["code_blocks"])

        assert len(all_code_blocks) >= 1  # Should have at least one code block

        # Check for expected content in code blocks
        all_code_content = " ".join(block["code"] for block in all_code_blocks)
        assert "pip install" in all_code_content or "import our_package" in all_code_content

        # Check that links are extracted
        all_links = []
        for chunk in result.chunks:
            all_links.extend(chunk["metadata"]["links"])
        assert len(all_links) >= 3

    def test_error_handling(self):
        """Test error handling during parsing."""
        # This test simulates an error by using a mock that raises an exception
        parser = MarkdownParser()

        # Mock the _parse_markdown method to raise an exception
        original_method = parser._parse_markdown
        def mock_parse_markdown(*args, **kwargs):  # noqa: ARG001
            raise ValueError("Simulated parsing error")

        parser._parse_markdown = mock_parse_markdown

        content = "# Test"
        file_path = Path("error_test.md")
        result = parser.parse(content, file_path)

        # Should fall back to simple parsing
        assert len(result.chunks) == 1
        assert len(result.errors) == 1
        assert "Simulated parsing error" in result.errors[0]

        # Restore original method
        parser._parse_markdown = original_method

    def test_line_numbers_accuracy(self):
        """Test that line numbers in chunks are accurate."""
        content = """# First Header
Line 2 content

## Second Header
Line 5 content
Line 6 content

### Third Header
Line 9 content
"""
        file_path = Path("line_numbers.md")
        result = self.parser.parse(content, file_path)

        assert len(result.chunks) == 3

        # Check that line numbers are reasonable and in order
        prev_end = 0
        for chunk in result.chunks:
            start_line = chunk["metadata"]["start_line"]
            end_line = chunk["metadata"]["end_line"]

            assert start_line >= 1
            assert end_line >= start_line
            assert start_line > prev_end  # No overlap
            prev_end = end_line

    def test_mixed_header_styles(self):
        """Test parsing markdown with mixed ATX and Setext headers."""
        content = """Main Title
==========

Content under main title.

## ATX Header

Content under ATX header.

Setext Header
-------------

Content under setext header.

### Another ATX

Final content.
"""
        file_path = Path("mixed_headers.md")
        result = self.parser.parse(content, file_path)

        assert len(result.chunks) == 4

        # Check header levels and titles
        headers = [(chunk["metadata"]["header_level"], chunk["metadata"]["header_title"])
                  for chunk in result.chunks]

        expected_headers = [
            (1, "Main Title"),
            (2, "ATX Header"),
            (2, "Setext Header"),
            (3, "Another ATX")
        ]

        assert headers == expected_headers


class TestMarkdownParserWithTestData:
    """Test suite using synthetic test data files."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = MarkdownParser()
        self.test_data_dir = Path(__file__).parent.parent / "data" / "markdown"

    def test_parse_simple_readme(self):
        """Test parsing a simple README file."""
        readme_path = self.test_data_dir / "simple_readme.md"
        content = readme_path.read_text()

        result = self.parser.parse(content, readme_path)

        assert len(result.chunks) >= 6  # At least 6 main sections
        assert len(result.errors) == 0

        # Check that we have the expected sections
        section_titles = [chunk["metadata"]["header_title"] for chunk in result.chunks]

        # Check that key sections are present
        assert "Simple Project" in section_titles
        assert "Installation" in section_titles
        assert "Usage" in section_titles

        # Check that code blocks are extracted
        installation_chunk = next(chunk for chunk in result.chunks
                                if chunk["metadata"]["header_title"] == "Installation")
        assert len(installation_chunk["metadata"]["code_blocks"]) >= 1

        usage_chunk = next(chunk for chunk in result.chunks
                         if chunk["metadata"]["header_title"] == "Usage")
        assert len(usage_chunk["metadata"]["code_blocks"]) >= 1

    def test_parse_api_docs(self):
        """Test parsing API documentation with complex structure."""
        api_docs_path = self.test_data_dir / "api_docs.md"
        content = api_docs_path.read_text()

        result = self.parser.parse(content, api_docs_path)

        assert len(result.chunks) >= 5  # Multiple sections
        assert len(result.errors) == 0

        # Check for code blocks in various sections
        total_code_blocks = sum(len(chunk["metadata"]["code_blocks"])
                              for chunk in result.chunks)
        assert total_code_blocks >= 4  # Should have multiple code examples

        # Check for different code languages
        all_code_blocks = []
        for chunk in result.chunks:
            all_code_blocks.extend(chunk["metadata"]["code_blocks"])

        languages = {block["language"] for block in all_code_blocks}
        assert "bash" in languages
        assert "json" in languages

    def test_parse_tutorial_with_setext_headers(self):
        """Test parsing tutorial with setext-style headers."""
        tutorial_path = self.test_data_dir / "tutorial.md"
        content = tutorial_path.read_text()

        result = self.parser.parse(content, tutorial_path)

        assert len(result.chunks) >= 4  # Multiple sections
        assert len(result.errors) == 0

        # Check that setext headers are parsed correctly
        first_chunk = result.chunks[0]
        assert first_chunk["metadata"]["header_level"] == 1
        assert first_chunk["metadata"]["header_title"] == "Getting Started Tutorial"

        # Check for mixed code block types (fenced and indented)
        all_code_blocks = []
        for chunk in result.chunks:
            all_code_blocks.extend(chunk["metadata"]["code_blocks"])

        code_types = {block["type"] for block in all_code_blocks}
        assert "fenced" in code_types
        assert "indented" in code_types

    def test_parse_no_headers_file(self):
        """Test parsing file without headers."""
        no_headers_path = self.test_data_dir / "no_headers.md"
        content = no_headers_path.read_text()

        result = self.parser.parse(content, no_headers_path)

        assert len(result.chunks) == 1  # Single chunk
        assert len(result.errors) == 0

        chunk = result.chunks[0]
        assert chunk["metadata"]["chunk_type"] == "documentation_file"

        # Should still extract code blocks and links
        assert len(chunk["metadata"]["code_blocks"]) >= 1
        assert len(chunk["metadata"]["links"]) >= 2

    def test_parse_rst_file(self):
        """Test parsing reStructuredText file."""
        rst_path = self.test_data_dir / "sample.rst"
        content = rst_path.read_text()

        result = self.parser.parse(content, rst_path)

        assert len(result.chunks) == 1  # RST parsing is simplified
        assert len(result.errors) == 0
        assert result.metadata["file_type"] == "rst"

        chunk = result.chunks[0]
        assert chunk["metadata"]["language"] == "rst"
        assert chunk["content"] == content

    def test_file_path_metadata(self):
        """Test that file path metadata is correctly set."""
        readme_path = self.test_data_dir / "simple_readme.md"
        content = readme_path.read_text()

        result = self.parser.parse(content, readme_path)

        # Check result metadata
        assert result.metadata["file_path"] == str(readme_path)

        # Check chunk metadata
        for chunk in result.chunks:
            assert chunk["metadata"]["file_path"] == str(readme_path)

    def test_chunk_line_numbers(self):
        """Test that chunk line numbers are accurate for real files."""
        tutorial_path = self.test_data_dir / "tutorial.md"
        content = tutorial_path.read_text()
        lines = content.split('\n')

        result = self.parser.parse(content, tutorial_path)

        # Verify that line numbers make sense
        for chunk in result.chunks:
            start_line = chunk["metadata"]["start_line"]
            end_line = chunk["metadata"]["end_line"]

            assert start_line >= 1
            assert end_line <= len(lines)
            assert start_line <= end_line

            # Check that the chunk content matches the line range
            chunk_lines = lines[start_line-1:end_line]
            expected_content = '\n'.join(chunk_lines)
            assert chunk["content"] == expected_content